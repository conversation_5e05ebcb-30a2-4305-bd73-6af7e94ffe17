#!/usr/bin/env python3
"""
Demo script to show the vectorized reverse mapping logic for futures pseudo expiry.
"""

import pandas as pd
import numpy as np

def demo_vectorized_reverse_mapping():
    """Demonstrate the fully vectorized reverse mapping logic."""
    
    print("Demo: Fully Vectorized Reverse Mapping")
    print("=" * 60)
    
    # Example data
    option_expiries = pd.to_datetime(['2024-11-20', '2024-12-20', '2025-01-15'])
    futures_expiries = pd.to_datetime(['2024-11-29', '2025-02-28', '2025-04-30', '2025-06-30'])
    
    print("Option expiries: ", option_expiries.strftime('%Y-%m-%d').tolist())
    print("Futures expiries:", futures_expiries.strftime('%Y-%m-%d').tolist())
    print()
    
    # Normalize and sort
    unique_option_expiries = np.sort(option_expiries.normalize().unique())
    unique_futures_expiries = np.sort(futures_expiries.normalize().unique())
    
    print("Step 1: Find indices using searchsorted")
    print("-" * 40)
    
    # Find indices of nearest futures expiry >= each option expiry
    indices = np.searchsorted(unique_futures_expiries, unique_option_expiries, side='left')
    
    print("Option expiries:     ", pd.to_datetime(unique_option_expiries).strftime('%Y-%m-%d').tolist())
    print("Searchsorted indices:", indices.tolist())
    print("Corresponding futures:", [
        pd.to_datetime(unique_futures_expiries[i]).strftime('%Y-%m-%d') if i < len(unique_futures_expiries) else 'OUT_OF_BOUNDS'
        for i in indices
    ])
    print()
    
    print("Step 2: Filter valid indices")
    print("-" * 40)
    
    # Filter out indices that are out of bounds
    valid_mask = indices < len(unique_futures_expiries)
    valid_indices = indices[valid_mask]
    valid_option_expiries = unique_option_expiries[valid_mask]
    
    print("Valid mask:          ", valid_mask.tolist())
    print("Valid indices:       ", valid_indices.tolist())
    print("Valid option expiries:", pd.to_datetime(valid_option_expiries).strftime('%Y-%m-%d').tolist())
    print()
    
    print("Step 3: Handle duplicate indices (one-to-one mapping)")
    print("-" * 40)
    
    # Use unique indices to ensure one-to-one mapping
    unique_valid_indices, first_occurrence = np.unique(valid_indices, return_index=True)
    
    print("Unique valid indices:", unique_valid_indices.tolist())
    print("First occurrence:    ", first_occurrence.tolist())
    print("Selected options:    ", pd.to_datetime(valid_option_expiries[first_occurrence]).strftime('%Y-%m-%d').tolist())
    print()
    
    print("Step 4: Create final mapping")
    print("-" * 40)
    
    # Initialize result mapping array with NaN
    result_mapping = np.full(len(unique_futures_expiries), pd.NaT, dtype='datetime64[ns]')
    
    # Map valid option expiries to their corresponding futures expiries
    result_mapping[unique_valid_indices] = valid_option_expiries[first_occurrence]
    
    print("Final mapping results:")
    for i, (futures_exp, mapped_option) in enumerate(zip(unique_futures_expiries, result_mapping)):
        futures_str = pd.to_datetime(futures_exp).strftime('%Y-%m-%d')
        if pd.isna(mapped_option):
            option_str = "NaN (unmapped)"
        else:
            option_str = pd.to_datetime(mapped_option).strftime('%Y-%m-%d')
        
        print(f"  Futures {futures_str} -> Option {option_str}")
    
    print("\n" + "=" * 60)
    print("Vectorized vs Loop Comparison:")
    print("- Vectorized: All operations done with numpy arrays")
    print("- No explicit Python loops over data")
    print("- Uses np.searchsorted, np.unique, boolean indexing")
    print("- Much faster for large datasets")
    
    return result_mapping


def compare_performance():
    """Compare performance between loop and vectorized approaches."""
    import time
    
    print("\n" + "=" * 60)
    print("Performance Comparison (Large Dataset)")
    print("-" * 40)
    
    # Create larger test data
    np.random.seed(42)
    n_options = 1000
    n_futures = 5000
    
    base_date = pd.Timestamp('2024-01-01')
    option_expiries = base_date + pd.to_timedelta(np.random.randint(0, 365*2, n_options), unit='D')
    futures_expiries = base_date + pd.to_timedelta(np.random.randint(0, 365*3, n_futures), unit='D')
    
    unique_option_expiries = np.sort(option_expiries.normalize().unique())
    unique_futures_expiries = np.sort(futures_expiries.normalize().unique())
    
    print(f"Dataset size: {len(unique_option_expiries)} options, {len(unique_futures_expiries)} futures")
    
    # Vectorized approach
    start_time = time.time()
    
    indices = np.searchsorted(unique_futures_expiries, unique_option_expiries, side='left')
    valid_mask = indices < len(unique_futures_expiries)
    valid_indices = indices[valid_mask]
    valid_option_expiries_subset = unique_option_expiries[valid_mask]
    result_mapping = np.full(len(unique_futures_expiries), pd.NaT, dtype='datetime64[ns]')
    unique_valid_indices, first_occurrence = np.unique(valid_indices, return_index=True)
    result_mapping[unique_valid_indices] = valid_option_expiries_subset[first_occurrence]
    
    vectorized_time = time.time() - start_time
    
    # Loop approach (for comparison)
    start_time = time.time()
    
    result_mapping_loop = np.full(len(unique_futures_expiries), pd.NaT, dtype='datetime64[ns]')
    for option_exp in unique_option_expiries:
        idx = np.searchsorted(unique_futures_expiries, option_exp, side='left')
        if idx < len(unique_futures_expiries) and pd.isna(result_mapping_loop[idx]):
            result_mapping_loop[idx] = option_exp
    
    loop_time = time.time() - start_time
    
    print(f"Vectorized approach: {vectorized_time:.4f} seconds")
    print(f"Loop approach:       {loop_time:.4f} seconds")
    print(f"Speedup:             {loop_time/vectorized_time:.2f}x faster")
    
    # Verify results are the same
    results_match = np.array_equal(result_mapping, result_mapping_loop, equal_nan=True)
    print(f"Results match:       {results_match}")


if __name__ == "__main__":
    demo_vectorized_reverse_mapping()
    compare_performance()
