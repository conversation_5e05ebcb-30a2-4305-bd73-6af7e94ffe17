from multiprocessing import Pool
import os
import pickle
import pandas as pd
from main.tanki import Tanki
from arcticdb import Arctic


# tanki = Tanki(exchange_type="nse")
# tanki.login(username="mantraraj", password="mantraraj")
tanki = Tanki(exchange_type="mcx", write=True)
tanki.login(username="mantraraj", password="mantraraj")


mssg = tanki.compile_data(
    universe_list=["mcx_fut_near", "optcom"],
    frequency=1,
    dtype="trd",
    start_date=pd.Timestamp(2024, 10, 10),
    end_date=pd.Timestamp(2024, 10, 20),
)

print(mssg)


# from main.config.config_NSE import ConfigNSE
# from main.config.config_MCX import ConfigMCX
# from main.data.operation import Operator

# opt=Operator(config=ConfigNSE())
# opt_mcx=Operator(config=ConfigMCX())

# typ='opt'
# sym='GOLDM'

# df=pd.read_parquet(f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/{typ}_final.parquet")
# tanki["mcx/1_min/raw_optcom_oi/trd"].write_metadata(sym, df)
# tanki["mcx/1_min/raw_optcom_oi/trd"].write(sym, df)


# symbol_to_balte_id = {}
# with open('/home/<USER>/repos/data_auditing/symbol_to_balte_id', 'rb') as f:
#     symbol_to_balte_id = pickle.load(f)





def push_to_kivi(sym):
    if os.path.exists(
        f"/home/<USER>/repos/data_auditing/success_cash_5min_push/{sym}"
    ):
        return

    try:
        print(f"Started for {sym}")
        storek = Arctic(
            "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        store = Arctic(
            "s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret"
        )

        tanki = Tanki(exchange_type="nse", write=True)
        tanki.login(username="airflow", password="airflow")

        dfn = (
            store["nse/compilation/cash"]
            .read(f"nse_5min_cash_trd_{sym}_20070101_20250724")
            .data
        )

        storek["nse/5_min/cash/trd"].delete(sym)

        tanki["nse/5_min/cash/trd"].write_metadata(sym, dfn)
        tanki["nse/5_min/cash/trd"].write(sym, dfn)

        os.makedirs(f"/home/<USER>/repos/data_auditing/success_cash_5min_push/{sym}")
        print(f"Pushed {sym}")
    except Exception as e:
        with open(
            f"/home/<USER>/repos/data_auditing/failed_cash_5min_push/{sym}.txt", "w"
        ) as f:
            f.write(f"Failed for {sym} due to {e}")


def fix_2018_1_1_issue(sym):
    if os.path.exists(
        f"/home/<USER>/repos/data_auditing/success_cash_1min_fix_20180101/{sym}"
    ):
        return

    try:
        storek = Arctic(
            "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        libc = storek["nse/1_min/cash/trd"]

        tanki = Tanki(exchange_type="nse", write=True)
        tanki.login(username="airflow", password="airflow")

        df = libc.read(sym).data

        dfafter = df[df.index.date > pd.Timestamp(2018, 1, 1).date()]

        dfbefore = df[df.index.date < pd.Timestamp(2018, 1, 1).date()]

        dfon = df[df.index.date == pd.Timestamp(2018, 1, 1).date()]

        if len(dfon) == 0:
            return

        if len(dfbefore):
            prevclose = dfbefore.Close[-1]
        elif len(dfafter):
            prevclose = dfafter.Close[0]
        else:
            prevclose = dfon.Close[0]

        dfon["prev_close"] = prevclose
        dfon["pct_change_close"] = abs(dfon.Close - prevclose)
        dfon["pct_change_close"] = dfon["pct_change_close"] / dfon[
            ["prev_close", "Close"]
        ].min(axis=1)
        dfon["pct_change_close"] = dfon["pct_change_close"] * 100

        dfon = dfon[dfon.pct_change_close <= 10]

        dfon = dfon.drop(columns=["prev_close", "pct_change_close"])

        df = pd.concat([dfbefore, dfon, dfafter])

        libc.delete(sym)

        tanki["nse/1_min/cash/trd"].write_metadata(sym, df)
        tanki["nse/1_min/cash/trd"].write(sym, df)

        os.makedirs(
            f"/home/<USER>/repos/data_auditing/success_cash_1min_fix_20180101/{sym}"
        )
    except Exception as e:
        os.makedirs(
            f"/home/<USER>/repos/data_auditing/failed_cash_1min_fix_20180101/{sym}"
        )
        print(f"Failed for {sym} due to {e}")


def push_cb(universe, frequency, sym):
    try:
        print(f"started for {sym}")
        store = Arctic(
            "s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret"
        )
        storek = Arctic(
            "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        libcom = store[f"nse/compilation/{universe}/{frequency}"]
        lib = storek[f"nse/{frequency}_min/{universe}/column_bucket"]

        tanki = Tanki(exchange_type="nse", write=True)
        tanki.login(username="airflow", password="airflow")

        cb_list = []
        for s in libcom.list_symbols():
            if f"nse_{frequency}min_{universe}_{sym}_" in s:
                cb_list.append(libcom.read(s).data)

        df = pd.concat(cb_list)
        df = df.sort_index()
        lib.delete(sym)

        tanki[f"nse/{frequency}_min/{universe}/column_bucket"].write_metadata(sym, df)
        tanki[f"nse/{frequency}_min/{universe}/column_bucket"].write(sym, df)

        print(f"Done for {sym}")
    except Exception as e:
        print(f"Failed for {sym} due to {e}")


def fix_id_and_push_kivi(sym):
    if os.path.exists(
        f"/home/<USER>/repos/data_auditing/success_raw_circuit_fix_id/{sym}"
    ):
        return
    
    if sym not in symbol_to_balte_id:
        return
    
    try:
        print(f"Started for {sym}")
        storek = Arctic(
            "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )
        store = Arctic(
            "s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret"
        )

        tanki = Tanki(exchange_type="nse", write=True)
        tanki.login(username="airflow", password="airflow")

        df = store["nse/1_min/raw_circuit/trd"].read(sym).data
        df["ID"] = symbol_to_balte_id[sym]
        df["ID"] = df["ID"].astype("uint64")

        df=df.rename(columns={"upper":"upper_circuit",'lower':'lower_circuit'})
        df=df[['ID', 'symbol','upper_circuit', 'lower_circuit']]


        # applying demerger merger
        libafter = storek["nse/1440_min/after_market/trd"]
        demerger_merger = libafter.read("demerger_merger").data
        symbol_change = libafter.read('symbol_change').data
        
        symbol_change = symbol_change[symbol_change.symbol == sym]
        
        if len(symbol_change) == 0:
            curr_sym = sym
        else:
            curr_sym = symbol_change.iloc[0, 1]
            
        different_IDs = demerger_merger[demerger_merger["Symbol"] == curr_sym]

        different_IDs = different_IDs.reset_index()

        for ind in range(len(different_IDs) - 1, -1, -1):
            exdate = different_IDs.iloc[ind].exdate
            df.loc[df.index < exdate, "ID"] = different_IDs.iloc[ind].ID

        df.index.name = "date"
        tanki["nse/1440_min/raw_circuit/trd"].write_metadata(sym, df)
        tanki["nse/1440_min/raw_circuit/trd"].write(sym, df)

        os.makedirs(f"/home/<USER>/repos/data_auditing/success_raw_circuit_fix_id/{sym}")
        print(f"Done for {sym}")
    except Exception as e:
        with open(
            f"/home/<USER>/repos/data_auditing/failed_raw_circuit_fix_id/{sym}.txt", "w"
        ) as f:
            f.write(f"Failed for {sym} due to {e}")




# store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")
# libcom = store["nse/compilation/cash"]

# syms = [
#     s.split("_")[4]
#     for s in libcom.list_symbols()
#     if "nse_5min_cash_trd_" in s and "20070101_20250724" in s
# ]

# with Pool(18) as p:
#     p.map(push_to_kivi, syms)

# [push_to_kivi(s) for s in os.listdir("/home/<USER>/repos/data_auditing/failed_cash_1min_push")]


# fix_2018_1_1_issue('1022')

# syms = tanki["nse/1_min/cash/trd"].list_symbols()

# with Pool(25) as p:
#     p.map(fix_2018_1_1_issue, syms)


# [push_cb("fno", 5, s) for s in ['High', 'Cons_Volume', 'Open', 'Close', 'Low', 'Vwap']]


# fix_id_and_push_kivi("PEL")

# store=Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")
# lib=store["nse/1_min/raw_circuit/trd"]
# syms=lib.list_symbols()

# with Pool(25) as p:
#     p.map(fix_id_and_push_kivi, syms)


print()
