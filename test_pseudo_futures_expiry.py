#!/usr/bin/env python3
"""
Test script to demonstrate the modified __generate_pseudo_futures_expiry function
that uses reverse mapping: for each option expiry, finds nearest futures expiry >= option expiry.
"""

import pandas as pd
import numpy as np
from main.data.operation import Operator
from main.config.config_factory import ConfigFactory


def create_test_data():
    """Create test data for futures and options with specific expiry dates for reverse mapping test."""

    # Create futures data with multiple expiry dates to test reverse mapping
    futures_data = pd.DataFrame({
        'symbol': ['GOLD'] * 4,
        'expiry': pd.to_datetime(['2024-11-29', '2025-02-28', '2025-04-30', '2025-06-30']),
        'Close': [50000, 51000, 52000, 53000]
    })
    futures_data.index = pd.to_datetime(['2024-10-01 10:00:00'] * 4)
    futures_data.index.name = 'timestamp'

    # Create options data with expiry dates: 2024-11-20, 2024-12-20
    # These should map to nearest futures >= option expiry
    options_data = pd.DataFrame({
        'symbol': ['GOLD'] * 4,
        'expiry': pd.to_datetime(['2024-11-20', '2024-11-20', '2024-12-20', '2024-12-20']),
        'strike': [49000, 51000, 50000, 52000],
        'option_type': [1, 0, 1, 0],  # 1 for call, 0 for put
        'Close': [1000, 500, 1200, 600]
    })
    options_data.index = pd.to_datetime(['2024-10-01 10:00:00'] * 4)
    options_data.index.name = 'timestamp'

    # Create all_dates list
    all_dates = pd.date_range('2024-09-01', '2025-12-31', freq='D').tolist()

    return futures_data, options_data, all_dates


def test_reverse_mapping():
    """Test the reverse mapping functionality (option expiry -> nearest futures expiry >= option expiry)."""

    print("Testing reverse mapping functionality...")
    print("=" * 60)

    # Create test data
    futures_data, options_data, all_dates = create_test_data()

    print("Original Futures Data:")
    print(futures_data[['expiry', 'Close']])
    print("\nOptions Data (unique expiries):")
    print(options_data[['expiry']].drop_duplicates().sort_values('expiry'))

    # Create a mock config for testing
    config = ConfigFactory("mcx")

    # Create operator
    operator = Operator(config=config)

    # Test the function directly for a symbol NOT in FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES
    result = operator._Operator__generate_pseudo_futures_expiry(
        universe='optcom',
        symbol='7252',  # Symbol NOT configured in offset rules, so will use reverse mapping
        underlying_df=futures_data.copy(),
        option_df=options_data,
        all_dates=all_dates
    )

    print("\nResults after applying reverse mapping (option expiry -> nearest futures >= option expiry):")
    print("=" * 60)
    print("Original futures expiry -> Mapped option expiry (or NaN)")

    original_expiries = futures_data['expiry'].dt.strftime('%Y-%m-%d').tolist()
    new_expiries = []
    for exp in result['expiry']:
        if pd.isna(exp):
            new_expiries.append('NaN')
        else:
            new_expiries.append(exp.strftime('%Y-%m-%d'))

    for orig, new in zip(original_expiries, new_expiries):
        print(f"{orig} -> {new}")

    print("\nExpected reverse mapping logic:")
    print("Option expiries: 2024-11-20, 2024-12-20")
    print("Futures expiries: 2024-11-29, 2025-02-28, 2025-04-30, 2025-06-30")
    print("2024-11-20 -> 2024-11-29 (nearest futures >= 2024-11-20)")
    print("2024-12-20 -> 2025-02-28 (nearest futures >= 2024-12-20)")
    print("Unmapped futures (2025-04-30, 2025-06-30) -> NaN")

    # Verify the results
    expected_new_expiries = ['2024-11-20', '2024-12-20', 'NaN', 'NaN']

    print(f"\nTest Result: {'PASSED' if new_expiries == expected_new_expiries else 'FAILED'}")

    if new_expiries != expected_new_expiries:
        print(f"Expected: {expected_new_expiries}")
        print(f"Actual: {new_expiries}")

    return result


def test_offset_method():
    """Test the original offset method for comparison."""
    
    print("\n\nTesting original offset method...")
    print("=" * 60)
    
    # Create test data
    futures_data, options_data, all_dates = create_test_data()
    
    # Create config with offset rule
    config = ConfigFactory("mcx")
    
    # Ensure the offset rule exists
    if not hasattr(config, 'FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES'):
        config.FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES = {}
    
    config.FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES['optcom'] = {'7251': 7}
    
    # Create operator
    operator = Operator(config=config)
    
    # Test the function with offset method
    result = operator._Operator__generate_pseudo_futures_expiry(
        universe='optcom',
        symbol='7251',  # Symbol configured to use offset method
        underlying_df=futures_data.copy(),
        option_df=options_data,
        all_dates=all_dates
    )
    
    print("Results using offset method (offset=7):")
    original_expiries = futures_data['expiry'].dt.strftime('%Y-%m-%d').tolist()
    new_expiries = result['expiry'].dt.strftime('%Y-%m-%d').tolist()
    
    for orig, new in zip(original_expiries, new_expiries):
        print(f"{orig} -> {new}")


if __name__ == "__main__":
    try:
        # Test reverse mapping method
        test_reverse_mapping()
        
        # Test original offset method
        test_offset_method()
        
        print("\n" + "=" * 60)
        print("All tests completed!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
