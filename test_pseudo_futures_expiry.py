#!/usr/bin/env python3
"""
Test script to demonstrate the modified __generate_pseudo_futures_expiry function
that uses nearest option expiry for certain symbols.
"""

import pandas as pd
import numpy as np
from main.data.operation import Operator
from main.config.config_factory import ConfigFactory


def create_test_data():
    """Create test data for futures and options with specific expiry dates."""

    # Create futures data with expiry dates: 15-10-2024, 16-11-2024
    futures_data = pd.DataFrame({
        'symbol': ['GOLD', 'GOLD'],
        'expiry': pd.to_datetime(['2024-10-15', '2024-11-16']),
        'Close': [50000, 51000]
    })
    futures_data.index = pd.to_datetime(['2024-10-01 10:00:00', '2024-10-01 10:00:00'])
    futures_data.index.name = 'timestamp'

    # Create options data with expiry dates: 1-10-2024, 10-11-2024
    # Adding more option expiries to test the "largest <= futures expiry" logic
    options_data = pd.DataFrame({
        'symbol': ['GOLD'] * 6,
        'expiry': pd.to_datetime(['2024-09-25', '2024-10-01', '2024-10-10', '2024-11-05', '2024-11-10', '2024-11-20']),
        'strike': [49000, 51000, 50000, 52000, 48000, 53000],
        'option_type': [1, 0, 1, 0, 1, 0],  # 1 for call, 0 for put
        'Close': [1000, 500, 1200, 600, 800, 400]
    })
    options_data.index = pd.to_datetime(['2024-10-01 10:00:00'] * 6)
    options_data.index.name = 'timestamp'

    # Create all_dates list
    all_dates = pd.date_range('2024-09-01', '2024-12-31', freq='D').tolist()

    return futures_data, options_data, all_dates


def test_nearest_option_expiry():
    """Test the nearest option expiry functionality (largest option expiry <= futures expiry)."""

    print("Testing nearest option expiry functionality...")
    print("=" * 60)

    # Create test data
    futures_data, options_data, all_dates = create_test_data()

    print("Original Futures Data:")
    print(futures_data[['expiry', 'Close']])
    print("\nOptions Data (unique expiries):")
    print(options_data[['expiry']].drop_duplicates().sort_values('expiry'))

    # Create a mock config for testing
    config = ConfigFactory("mcx")

    # Create operator
    operator = Operator(config=config)

    # Test the function directly for a symbol NOT in FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES
    result = operator._Operator__generate_pseudo_futures_expiry(
        universe='optcom',
        symbol='7252',  # Symbol NOT configured in offset rules, so will use nearest method
        underlying_df=futures_data.copy(),
        all_dates=all_dates,
        option_df=options_data
    )

    print("\nResults after applying nearest option expiry (largest <= futures expiry):")
    print("=" * 60)
    print("Original futures expiry -> Nearest option expiry")

    original_expiries = futures_data['expiry'].dt.strftime('%Y-%m-%d').tolist()
    new_expiries = result['expiry'].dt.strftime('%Y-%m-%d').tolist()

    for orig, new in zip(original_expiries, new_expiries):
        print(f"{orig} -> {new}")

    print("\nExpected mapping (largest option expiry <= futures expiry):")
    print("Available option expiries: 2024-09-25, 2024-10-01, 2024-10-10, 2024-11-05, 2024-11-10, 2024-11-20")
    print("2024-10-15 -> 2024-10-10 (largest option expiry <= 15-Oct is 10-Oct)")
    print("2024-11-16 -> 2024-11-10 (largest option expiry <= 16-Nov is 10-Nov)")

    # Verify the results
    expected_new_expiries = ['2024-10-10', '2024-11-10']
    actual_new_expiries = [exp.strftime('%Y-%m-%d') for exp in result['expiry']]

    print(f"\nTest Result: {'PASSED' if actual_new_expiries == expected_new_expiries else 'FAILED'}")

    if actual_new_expiries != expected_new_expiries:
        print(f"Expected: {expected_new_expiries}")
        print(f"Actual: {actual_new_expiries}")

    return result


def test_offset_method():
    """Test the original offset method for comparison."""
    
    print("\n\nTesting original offset method...")
    print("=" * 60)
    
    # Create test data
    futures_data, options_data, all_dates = create_test_data()
    
    # Create config with offset rule
    config = ConfigFactory("mcx")
    
    # Ensure the offset rule exists
    if not hasattr(config, 'FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES'):
        config.FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES = {}
    
    config.FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES['optcom'] = {'7251': 7}
    
    # Create operator
    operator = Operator(config=config)
    
    # Test the function with offset method
    result = operator._Operator__generate_pseudo_futures_expiry(
        universe='optcom',
        symbol='7251',  # Symbol configured to use offset method
        underlying_df=futures_data.copy(),
        all_dates=all_dates,
        option_df=options_data
    )
    
    print("Results using offset method (offset=7):")
    original_expiries = futures_data['expiry'].dt.strftime('%Y-%m-%d').tolist()
    new_expiries = result['expiry'].dt.strftime('%Y-%m-%d').tolist()
    
    for orig, new in zip(original_expiries, new_expiries):
        print(f"{orig} -> {new}")


if __name__ == "__main__":
    try:
        # Test nearest option expiry method
        test_nearest_option_expiry()
        
        # Test original offset method
        test_offset_method()
        
        print("\n" + "=" * 60)
        print("All tests completed!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
