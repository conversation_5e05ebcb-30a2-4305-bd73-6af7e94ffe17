from typing import Dict, <PERSON>, Tuple, Union
from main.config.config_base import ConfigBase
from dataclasses import dataclass, field
from main.enums import Check, ExchangeType


@dataclass
class ConfigMCX(ConfigBase):
    EXCHANGE = "mcx"
    EXCHANGE_ENUM = 3
    FILE_STORAGE = ""
    DB_STORAGE = ""

    FILE_DICT: Dict[str, Tuple[str, str]] = field(
        default_factory=lambda: {
            "ALL_DATES": ("commondata", "balte_uploads/ALL_DATES_MCX.npy"),
            "MAPPING_DICT": ("commondata", "balte_uploads/mapping_dict"),
            "DAYLIGHT_SAVING_DAYS": (
                "commondata",
                "balte_uploads/mcx_daylight_savings.csv",
            ),
        }
    )

    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "raw_mcx_spot": {"open": "9:00", "close": "23:55"},
            "mcx_spot": {"open": "9:00", "close": "23:55"},
            "raw_mcx_fut_near": {"open": "9:00", "close": "23:55"},
            "mcx_fut_near": {"open": "9:00", "close": "23:55"},
            "optcom": {"open": "9:00", "close": "23:55"},
            "raw_optcom": {"open": "9:00", "close": "23:55"},
            "raw_optcom_oi": {"open": "9:00", "close": "23:55"},
        }
    )

    PRE_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_mcx_spot": [],
            "mcx_spot": ["raw_mcx_spot", "raw_optcom_oi"],
            "raw_mcx_fut_near": [],
            "mcx_fut_near": ["raw_mcx_fut_near", "raw_optcom_oi"],
            "raw_optcom": [],
            "optcom": ["raw_optcom", "raw_mcx_fut_near", "raw_optcom_oi"],
            "raw_optcom_oi": [],
        }
    )

    POST_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_mcx_spot": ["mcx_spot"],
            "mcx_spot": [],
            "raw_mcx_fut_near": ["optcom", "mcx_fut_near"],
            "mcx_fut_near": [],
            "raw_optcom": ["optcom"],
            "optcom": [],
            "raw_optcom_oi": ["optcom", "mcx_fut_near", "mcx_spot"],
        }
    )

    COLUMNS_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_mcx_spot": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "mcx_spot": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "pcr",
            ],
            "raw_mcx_fut_near": [
                "timestamp",
                "ID",
                "symbol",
                "expiry",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "mcx_fut_near": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "pcr",
            ],
            "raw_optcom": [
                "timestamp",
                "ID",
                "symbol",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Vwap",
            ],
            "optcom": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Vwap",
                "OI",
                "iv",
                "delta",
                "gamma",
                "vega",
                "theta",
            ],
            "raw_optcom_oi": [
                "timestamp",
                "ID",
                "symbol",
                "expiry",
                "option_type",
                "strike",
                "OI",
            ],
            "optcom_oi": ["timestamp", "ID", "OI"],
            "mcx_bhav": [
                "date",
                "instrument name",
                "symbol",
                "expiry date",
                "option type",
                "strike price",
                "open",
                "high",
                "low",
                "close",
                "previous close",
                "volume(lots)",
                "volume(in 000's)",
                "value(lacs)",
                "open interest(lots)",
                "eod_price",
            ],
            "raw_mcx_bhav": [
                "date",
                "instrument name",
                "symbol",
                "expiry date",
                "option type",
                "strike price",
                "open",
                "high",
                "low",
                "close",
                "previous close",
                "volume(lots)",
                "volume(in 000's)",
                "value(lacs)",
                "open interest(lots)",
            ],
            "mcx_fut_bhav": [
                "date",
                "symbol",
                "expiry_date",
                "balte_id",
                "open",
                "high",
                "low",
                "close",
                "volume_in_lots",
                "turnover_in_lacs",
                "open_interest",
                "expiry_rank",
                "eod_price",
            ],
            "mcx_index_bhav": [
                "date",
                "symbol",
                "expiry_date",
                "option_type",
                "strike_price",
                "balte_id",
                "open",
                "high",
                "low",
                "close",
                "volume_in_lots",
                "turnover_in_lacs",
                "open_interest",
                "expiry_rank",
                "eod_price",
            ],
            "mcx_opt_bhav": [
                "date",
                "symbol",
                "expiry_date",
                "option_type",
                "strike_price",
                "balte_id",
                "open",
                "high",
                "low",
                "close",
                "volume_in_lots",
                "turnover_in_lacs",
                "open_interest",
                "expiry_rank",
                "eod_price",
            ],
            "mcx_lotsize": [
                "date",
                "ID",
                "near_month",
                "next_month",
            ],
            "opt_margin_mcx": [
                "date",
                "ID",
                "contract_price",
                "R1",
                "R2",
                "R3",
                "R4",
                "R5",
                "R6",
                "R7",
                "R8",
                "R9",
                "R10",
                "R11",
                "R12",
                "R13",
                "R14",
                "R15",
                "R16",
                "delta",
            ],
            "fut_margin_mcx": [
                "date",
                "ID",
                "span",
                "exposure",
                "tender",
                "delivery",
                "min_price",
                "additional_margin",
            ],
            "futcom_margin": [
                "date",
                "ID",
                "long_margin",
                "short_margin",
            ],
        }
    )

    UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT: Dict[
        str, Dict[Union[str, int, Tuple[str, str]], str]
    ] = field(
        default_factory=lambda: {
            "raw_mcx_spot": {"ID": "object"},
            "mcx_opt_bhav": {"option_type": "object"},
            "mcx_index_bhav": {"option_type": "object"},
            "mcx_bhav": {"option type": "object"},
            "raw_mcx_bhav": {"option type": "object"},
        }
    )

    UNIVERSE_TO_RENAME_COLUMN_DICT: Dict[str, Dict[Union[int, str], str]] = field(
        default_factory=lambda: {
            "mcx_fut_bhav": {
                "expiry date": "expiry_date",
                "option type": "option_type",
                "strike price": "strike_price",
                "previous close": "prev_close",
                "volume(lots)": "volume_in_lots",
                "volume(in 000's)": "volume_in_thousands",
                "value(lacs)": "turnover_in_lacs",
                "open interest(lots)": "open_interest",
                "instrument name": "instrument_name",
            },
            "mcx_opt_bhav": {
                "expiry date": "expiry_date",
                "option type": "option_type",
                "strike price": "strike_price",
                "previous close": "prev_close",
                "volume(lots)": "volume_in_lots",
                "volume(in 000's)": "volume_in_thousands",
                "value(lacs)": "turnover_in_lacs",
                "open interest(lots)": "open_interest",
                "instrument name": "instrument_name",
            },
            "mcx_index_bhav": {
                "expiry date": "expiry_date",
                "option type": "option_type",
                "strike price": "strike_price",
                "previous close": "prev_close",
                "volume(lots)": "volume_in_lots",
                "volume(in 000's)": "volume_in_thousands",
                "value(lacs)": "turnover_in_lacs",
                "open interest(lots)": "open_interest",
                "instrument name": "instrument_name",
            },
        }
    )

    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(
        default_factory=lambda: {
            "raw_mcx_fut_near": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_ALL_DATES,
            ],
            "mcx_fut_near": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_ALL_DATES,
            ],
            "raw_optcom": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_ALL_DATES,
            ],
            "raw_optcom_oi": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_ALL_DATES,
            ],
            "raw_mcx_fut_near_oi": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_ALL_DATES,
            ],
        }
    )

    UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_mcx_spot": [
                "symbol_change",
                "demerger_merger",
            ],
            "raw_mcx_fut_near": [
                "symbol_change",
                "demerger_merger",
            ],
            "raw_optcom": [
                "symbol_change",
                "demerger_merger",
            ],
            "raw_optcom_oi": [
                "symbol_change",
                "demerger_merger",
            ],
        }
    )

    UNIVERSE_TO_MINIO_FILES_LIST: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "mcx_fut_near": {"all_dates": "ALL_DATES"},
            "optcom": {
                "all_dates": "ALL_DATES",
                "daylight_saving_days": "DAYLIGHT_SAVING_DAYS",
            },
        }
    )

    OPTION_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_optcom",
            "optcom",
            "raw_optcom_oi",
            "optcom_oi",
        ]
    )

    FUTURE_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_mcx_fut_near",
            "mcx_fut_near",
        ]
    )

    SPOT_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_mcx_spot",
            "mcx_spot",
        ]
    )

    OPTION_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "raw_optcom": "raw_mcx_fut_near",
            "optcom": "mcx_fut_near",
        }
    )

    FUTURE_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "raw_mcx_fut_near": "raw_mcx_spot",
            "mcx_fut_near": "mcx_spot",
        }
    )

    UNDERLYING_TO_OPTION_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "raw_mcx_fut_near": "raw_optcom",
            "mcx_fut_near": "optcom",
        }
    )

    UNDERLYING_TO_FUTURE_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "raw_mcx_spot": "raw_mcx_fut_near",
            "mcx_spot": "mcx_fut_near",
        }
    )

    LOCAL_FILE_LOCATION = "mcx/compilation"

    INTEREST_RATE = 0.1
    EXPIRY_TIME = 86100  ## 23:30 in seconds
    EXPIRY_TIME_DAYLIGHT_SAVING_DAYS = 84600  ## 23:55 in seconds
    GREEKS_FROM_BLACK = True

    AFTER_MARKET_DICT_MINIO: Dict[str, str] = field(
        default_factory=lambda: {
            "mcx_bhav": "mcx_daily_downloads/mcx_bhav.csv",
            "mcx_fut_bhav": "mcx_daily_downloads/mcx_bhav.csv",
            "mcx_opt_bhav": "mcx_daily_downloads/mcx_bhav.csv",
            "mcx_index_bhav": "mcx_daily_downloads/mcx_bhav.csv",
            "raw_mcx_bhav": "mcx_daily_downloads/mcx_bhav.csv",
        }
    )

    AFTER_MARKET_DICT_GRPC: List[str] = field(
        default_factory=lambda: [
            "fut_margin_mcx",
            "futcom_margin",
            "mcx_lotsize",
            "opt_margin_mcx",
        ]
    )

    EXTRA_SYMBOL_TO_ID_DICT: Dict[str, int] = field(
        default_factory=lambda: {"GOLDM": 7251, "SILVERM": 7252}
    )

    LIBRARY_KEYWORDS_TO_SKIP_CHECK: List[str] = field(
        default_factory=lambda: [
            "column_bucket",
            "1440_min",
        ]
    )

    FUTURES_PSEUDO_EXPIRY_OFFSET_FROM_OPTIONS_RULES: Dict[str, Dict[str, int]] = field(
        default_factory=lambda: {
            "optcom": {'7251': 7},
        }
    )

    FUTURES_PSEUDO_EXPIRY_USE_NEAREST_OPTION: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "optcom": ['7252'],  # Example: SILVERM uses nearest option expiry
        }
    )
