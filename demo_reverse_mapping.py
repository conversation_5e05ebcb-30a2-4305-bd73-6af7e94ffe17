#!/usr/bin/env python3
"""
Demo script to show the reverse mapping logic for futures pseudo expiry.
"""

import pandas as pd
import numpy as np

def demo_reverse_mapping_logic():
    """Demonstrate the reverse mapping logic using pure pandas/numpy."""
    
    print("Demo: Reverse Mapping - Option Expiry -> Nearest Futures Expiry >= Option Expiry")
    print("=" * 80)
    
    # Example data from the requirements
    option_expiries = pd.to_datetime(['2024-11-20', '2024-12-20'])
    futures_expiries = pd.to_datetime(['2024-11-29', '2025-02-28', '2025-04-30', '2025-06-30'])
    
    print("Option expiries: ", option_expiries.strftime('%Y-%m-%d').tolist())
    print("Futures expiries:", futures_expiries.strftime('%Y-%m-%d').tolist())
    print()
    
    # Normalize to date only (remove time component)
    option_expiries_norm = option_expiries.normalize()
    futures_expiries_norm = futures_expiries.normalize()
    
    # Sort both arrays
    unique_option_expiries = np.sort(option_expiries_norm.unique())
    unique_futures_expiries = np.sort(futures_expiries_norm.unique())
    
    print("Sorted option expiries: ", pd.to_datetime(unique_option_expiries).strftime('%Y-%m-%d').tolist())
    print("Sorted futures expiries:", pd.to_datetime(unique_futures_expiries).strftime('%Y-%m-%d').tolist())
    print()
    
    # Initialize result array with NaN
    result_mapping = np.full(len(unique_futures_expiries), pd.NaT, dtype='datetime64[ns]')
    
    print("Reverse mapping process:")
    print("-" * 40)
    
    # For each option expiry, find the nearest futures expiry >= option expiry
    for i, option_exp in enumerate(unique_option_expiries):
        print(f"\nProcessing option expiry {i+1}: {pd.to_datetime(option_exp).strftime('%Y-%m-%d')}")
        
        # Find futures expiries >= current option expiry
        valid_futures_mask = unique_futures_expiries >= option_exp
        valid_futures = unique_futures_expiries[valid_futures_mask]
        
        if len(valid_futures) > 0:
            # Take the smallest (nearest) futures expiry >= option expiry
            nearest_futures_exp = valid_futures[0]
            
            # Find the index of this futures expiry in the original array
            futures_idx = np.where(unique_futures_expiries == nearest_futures_exp)[0][0]
            
            # Check if this futures expiry is already mapped
            if pd.isna(result_mapping[futures_idx]):
                result_mapping[futures_idx] = option_exp
                print(f"  Valid futures >= {pd.to_datetime(option_exp).strftime('%Y-%m-%d')}: {pd.to_datetime(valid_futures).strftime('%Y-%m-%d').tolist()}")
                print(f"  Mapped {pd.to_datetime(nearest_futures_exp).strftime('%Y-%m-%d')} -> {pd.to_datetime(option_exp).strftime('%Y-%m-%d')}")
            else:
                print(f"  Futures expiry {pd.to_datetime(nearest_futures_exp).strftime('%Y-%m-%d')} already mapped, skipping")
        else:
            print(f"  No futures expiry >= {pd.to_datetime(option_exp).strftime('%Y-%m-%d')}")
    
    print("\n" + "=" * 80)
    print("Final mapping results:")
    print("-" * 40)
    
    for i, (futures_exp, mapped_option) in enumerate(zip(unique_futures_expiries, result_mapping)):
        futures_str = pd.to_datetime(futures_exp).strftime('%Y-%m-%d')
        if pd.isna(mapped_option):
            option_str = "NaN (unmapped)"
        else:
            option_str = pd.to_datetime(mapped_option).strftime('%Y-%m-%d')
        
        print(f"Futures {futures_str} -> Option {option_str}")
    
    print("\n" + "=" * 80)
    print("Summary:")
    print("- Each option expiry maps to exactly one futures expiry (the nearest >= option expiry)")
    print("- Futures expiries that don't get mapped remain as NaN")
    print("- This ensures one-to-one mapping from option to futures")
    
    return result_mapping


if __name__ == "__main__":
    demo_reverse_mapping_logic()
