#!/usr/bin/env python3
"""
Demo script to show the modified __generate_pseudo_futures_expiry function behavior.
"""

import pandas as pd
import numpy as np

def demo_nearest_expiry_logic():
    """Demonstrate the nearest expiry logic using pure pandas/numpy."""
    
    print("Demo: Finding largest option expiry <= futures expiry")
    print("=" * 60)
    
    # Example data
    futures_expiries = pd.to_datetime(['2024-10-15', '2024-11-16'])
    option_expiries = pd.to_datetime(['2024-09-25', '2024-10-01', '2024-10-10', '2024-11-05', '2024-11-10', '2024-11-20'])
    
    print("Futures expiries:", futures_expiries.strftime('%Y-%m-%d').tolist())
    print("Option expiries: ", option_expiries.strftime('%Y-%m-%d').tolist())
    print()
    
    # Normalize to date only (remove time component)
    futures_expiries_norm = futures_expiries.normalize()
    option_expiries_norm = option_expiries.normalize()
    
    # Sort option expiries
    unique_option_expiries = np.sort(option_expiries_norm.unique())
    print("Sorted option expiries:", pd.to_datetime(unique_option_expiries).strftime('%Y-%m-%d').tolist())
    print()
    
    # For each futures expiry, find the largest option expiry <= futures expiry
    results = []
    for futures_exp in futures_expiries_norm:
        print(f"Finding largest option expiry <= {futures_exp.strftime('%Y-%m-%d')}:")
        
        # Find all option expiries <= futures expiry
        valid_options = unique_option_expiries[unique_option_expiries <= futures_exp]
        
        if len(valid_options) > 0:
            # Take the largest (last) one
            nearest_option_exp = valid_options[-1]
            print(f"  Valid options: {pd.to_datetime(valid_options).strftime('%Y-%m-%d').tolist()}")
            print(f"  Selected: {pd.to_datetime(nearest_option_exp).strftime('%Y-%m-%d')}")
        else:
            # If no option expiry <= futures expiry, take the first option expiry
            nearest_option_exp = unique_option_expiries[0]
            print(f"  No valid options, using first: {pd.to_datetime(nearest_option_exp).strftime('%Y-%m-%d')}")
        
        results.append(nearest_option_exp)
        print()
    
    print("Final mapping:")
    for fut_exp, opt_exp in zip(futures_expiries, results):
        print(f"{fut_exp.strftime('%Y-%m-%d')} -> {pd.to_datetime(opt_exp).strftime('%Y-%m-%d')}")
    
    print("\n" + "=" * 60)
    
    # Show the optimized vectorized approach
    print("Optimized vectorized approach using searchsorted:")
    indices = np.searchsorted(unique_option_expiries, futures_expiries_norm, side='right') - 1
    indices = np.maximum(indices, 0)  # Handle edge case
    
    vectorized_results = unique_option_expiries[indices]
    print("Results:", pd.to_datetime(vectorized_results).strftime('%Y-%m-%d').tolist())
    
    # Verify both methods give same results
    manual_results = [pd.to_datetime(r).strftime('%Y-%m-%d') for r in results]
    vector_results = pd.to_datetime(vectorized_results).strftime('%Y-%m-%d').tolist()
    
    print(f"Manual method:     {manual_results}")
    print(f"Vectorized method: {vector_results}")
    print(f"Results match: {manual_results == vector_results}")


if __name__ == "__main__":
    demo_nearest_expiry_logic()
